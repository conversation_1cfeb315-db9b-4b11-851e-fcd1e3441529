{"ast": null, "code": "var _jsxFileName = \"D:\\\\html\\\\training\\\\iti\\\\graduation_project\\\\src\\\\pages\\\\Register.js\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Register = () => {\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"py-5 container\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      className: \"text-center\",\n      children: \"Register Now\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 6,\n      columnNumber: 5\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      className: \"row g-3 bg-dark mt-5 mb-5 mx-5 my-5 \",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-6 col-\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"inputState\",\n            className: \"form-label text-light\",\n            children: \"First name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 10,\n            columnNumber: 3\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            className: \"form-control\",\n            placeholder: \"First name\",\n            \"aria-label\": \"First name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 11,\n            columnNumber: 5\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 9,\n          columnNumber: 3\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"inputState\",\n            className: \"form-label text-light\",\n            children: \"Last name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 14,\n            columnNumber: 3\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            className: \"form-control\",\n            placeholder: \"Last name\",\n            \"aria-label\": \"Last name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 15,\n            columnNumber: 5\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 13,\n          columnNumber: 3\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 1\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"inputEmail4\",\n          className: \"form-label text-light\",\n          children: \"Email\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 5\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"email\",\n          placeholder: \"<EMAIL>\",\n          className: \"form-control\",\n          id: \"inputEmail4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 5\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 3\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"inputPassword4\",\n          className: \"form-label text-light\",\n          children: \"Password\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 5\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"password\",\n          placeholder: \"Password\",\n          className: \"form-control\",\n          id: \"inputPassword4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 5\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 3\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"inputCity\",\n          className: \"form-label text-light\",\n          children: \"City\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 5\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          placeholder: \"City\",\n          className: \"form-control\",\n          id: \"inputCity\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 5\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 3\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"inputphone\",\n          className: \"form-label text-light\",\n          children: \"Phone\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 5\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          placeholder: \"Phone\",\n          className: \"form-control\",\n          id: \"inputCity\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 5\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 3\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mx-5\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-check form-switch\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            className: \"form-check-input \",\n            type: \"checkbox\",\n            role: \"switch\",\n            id: \"flexSwitchCheckDefault\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 5\n          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-check-label text-light\",\n            htmlFor: \"flexSwitchCheckDefault\",\n            children: \"Male\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 5\n          }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 5\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            className: \"form-check-input \",\n            type: \"checkbox\",\n            role: \"switch\",\n            id: \"flexSwitchCheckDefault\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 5\n          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-check-label text-light\",\n            htmlFor: \"flexSwitchCheckDefault\",\n            children: \"FeMale\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 5\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 3\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 1\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"btn btn-primary mx-3\",\n          children: \"Sign in\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 5\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"btn btn-danger\",\n          children: /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"Sign.js\",\n            children: \"Sign up\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 54\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 5\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 49,\n        columnNumber: 3\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 1\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this);\n};\n_c = Register;\nexport default Register;\nvar _c;\n$RefreshReg$(_c, \"Register\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Register", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "htmlFor", "type", "placeholder", "id", "role", "href", "_c", "$RefreshReg$"], "sources": ["D:/html/training/iti/graduation_project/src/pages/Register.js"], "sourcesContent": ["import React from 'react'\r\n\r\nconst Register = () =>{\r\n  return (\r\n    <section className='py-5 container'>\r\n    <h1 className='text-center'>Register Now</h1>\r\n<form className=\"row g-3 bg-dark mt-5 mb-5 mx-5 my-5 \">\r\n<div className=\"col-md-6 col-\">\r\n  <div className=\"col\">\r\n  <label htmlFor=\"inputState\" className=\"form-label text-light\">First name</label>\r\n    <input type=\"text\" className=\"form-control\" placeholder=\"First name\" aria-label=\"First name\" />\r\n  </div>\r\n  <div className=\"col\">\r\n  <label htmlFor=\"inputState\" className=\"form-label text-light\">Last name</label>\r\n    <input type=\"text\" className=\"form-control\" placeholder=\"Last name\" aria-label=\"Last name\" />\r\n  </div>\r\n</div>\r\n\r\n  <div className=\"col-md-6\">\r\n    <label htmlFor=\"inputEmail4\" className=\"form-label text-light\">Email</label>\r\n    <input type=\"email\"  placeholder=\"<EMAIL>\" className=\"form-control\" id=\"inputEmail4\" />\r\n  </div>\r\n  <div className=\"col-md-6\">\r\n    <label htmlFor=\"inputPassword4\" className=\"form-label text-light\">Password</label>\r\n    <input type=\"password\" placeholder=\"Password\" className=\"form-control\" id=\"inputPassword4\" />\r\n  </div>\r\n\r\n  <div className=\"col-md-6\">\r\n    <label htmlFor=\"inputCity\" className=\"form-label text-light\">City</label>\r\n    <input type=\"text\" placeholder=\"City\" className=\"form-control\" id=\"inputCity\" />\r\n  </div>\r\n  <div className=\"col-md-6\">\r\n    <label htmlFor=\"inputphone\" className=\"form-label text-light\">Phone</label>\r\n    <input type=\"text\" placeholder=\"Phone\" className=\"form-control\" id=\"inputCity\" />\r\n  </div>\r\n\r\n<div className='mx-5'>\r\n  <div className=\"form-check form-switch\">\r\n    <input className=\"form-check-input \" type=\"checkbox\" role=\"switch\" id=\"flexSwitchCheckDefault\" />\r\n    <label className=\"form-check-label text-light\" htmlFor=\"flexSwitchCheckDefault\">Male</label>\r\n    <br/>\r\n    <input className=\"form-check-input \" type=\"checkbox\" role=\"switch\" id=\"flexSwitchCheckDefault\"  />\r\n    <label className=\"form-check-label text-light\" htmlFor=\"flexSwitchCheckDefault\">FeMale</label>\r\n  </div>\r\n</div>\r\n\r\n\r\n\r\n  <div className=\"col-12\">\r\n    <button type=\"submit\" className=\"btn btn-primary mx-3\">Sign in</button>\r\n\r\n    <button type=\"submit\" className=\"btn btn-danger\"><a href=\"Sign.js\">Sign up</a></button>\r\n  </div>\r\n  \r\n</form>\r\n</section>\r\n  )\r\n}\r\n\r\nexport default Register;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEzB,MAAMC,QAAQ,GAAGA,CAAA,KAAK;EACpB,oBACED,OAAA;IAASE,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBACnCH,OAAA;MAAIE,SAAS,EAAC,aAAa;MAAAC,QAAA,EAAC;IAAY;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACjDP,OAAA;MAAME,SAAS,EAAC,sCAAsC;MAAAC,QAAA,gBACtDH,OAAA;QAAKE,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BH,OAAA;UAAKE,SAAS,EAAC,KAAK;UAAAC,QAAA,gBACpBH,OAAA;YAAOQ,OAAO,EAAC,YAAY;YAACN,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC9EP,OAAA;YAAOS,IAAI,EAAC,MAAM;YAACP,SAAS,EAAC,cAAc;YAACQ,WAAW,EAAC,YAAY;YAAC,cAAW;UAAY;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5F,CAAC,eACNP,OAAA;UAAKE,SAAS,EAAC,KAAK;UAAAC,QAAA,gBACpBH,OAAA;YAAOQ,OAAO,EAAC,YAAY;YAACN,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC7EP,OAAA;YAAOS,IAAI,EAAC,MAAM;YAACP,SAAS,EAAC,cAAc;YAACQ,WAAW,EAAC,WAAW;YAAC,cAAW;UAAW;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1F,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEJP,OAAA;QAAKE,SAAS,EAAC,UAAU;QAAAC,QAAA,gBACvBH,OAAA;UAAOQ,OAAO,EAAC,aAAa;UAACN,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC5EP,OAAA;UAAOS,IAAI,EAAC,OAAO;UAAEC,WAAW,EAAC,qBAAqB;UAACR,SAAS,EAAC,cAAc;UAACS,EAAE,EAAC;QAAa;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChG,CAAC,eACNP,OAAA;QAAKE,SAAS,EAAC,UAAU;QAAAC,QAAA,gBACvBH,OAAA;UAAOQ,OAAO,EAAC,gBAAgB;UAACN,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAClFP,OAAA;UAAOS,IAAI,EAAC,UAAU;UAACC,WAAW,EAAC,UAAU;UAACR,SAAS,EAAC,cAAc;UAACS,EAAE,EAAC;QAAgB;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1F,CAAC,eAENP,OAAA;QAAKE,SAAS,EAAC,UAAU;QAAAC,QAAA,gBACvBH,OAAA;UAAOQ,OAAO,EAAC,WAAW;UAACN,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACzEP,OAAA;UAAOS,IAAI,EAAC,MAAM;UAACC,WAAW,EAAC,MAAM;UAACR,SAAS,EAAC,cAAc;UAACS,EAAE,EAAC;QAAW;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7E,CAAC,eACNP,OAAA;QAAKE,SAAS,EAAC,UAAU;QAAAC,QAAA,gBACvBH,OAAA;UAAOQ,OAAO,EAAC,YAAY;UAACN,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC3EP,OAAA;UAAOS,IAAI,EAAC,MAAM;UAACC,WAAW,EAAC,OAAO;UAACR,SAAS,EAAC,cAAc;UAACS,EAAE,EAAC;QAAW;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9E,CAAC,eAERP,OAAA;QAAKE,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnBH,OAAA;UAAKE,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACrCH,OAAA;YAAOE,SAAS,EAAC,mBAAmB;YAACO,IAAI,EAAC,UAAU;YAACG,IAAI,EAAC,QAAQ;YAACD,EAAE,EAAC;UAAwB;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjGP,OAAA;YAAOE,SAAS,EAAC,6BAA6B;YAACM,OAAO,EAAC,wBAAwB;YAAAL,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC5FP,OAAA;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLP,OAAA;YAAOE,SAAS,EAAC,mBAAmB;YAACO,IAAI,EAAC,UAAU;YAACG,IAAI,EAAC,QAAQ;YAACD,EAAE,EAAC;UAAwB;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAClGP,OAAA;YAAOE,SAAS,EAAC,6BAA6B;YAACM,OAAO,EAAC,wBAAwB;YAAAL,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3F;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAIJP,OAAA;QAAKE,SAAS,EAAC,QAAQ;QAAAC,QAAA,gBACrBH,OAAA;UAAQS,IAAI,EAAC,QAAQ;UAACP,SAAS,EAAC,sBAAsB;UAAAC,QAAA,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAEvEP,OAAA;UAAQS,IAAI,EAAC,QAAQ;UAACP,SAAS,EAAC,gBAAgB;UAAAC,QAAA,eAACH,OAAA;YAAGa,IAAI,EAAC,SAAS;YAAAV,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAAO,EAAA,GAvDKb,QAAQ;AAyDd,eAAeA,QAAQ;AAAC,IAAAa,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}