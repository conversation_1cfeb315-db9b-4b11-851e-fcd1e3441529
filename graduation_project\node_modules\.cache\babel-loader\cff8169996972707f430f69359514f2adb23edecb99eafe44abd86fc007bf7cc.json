{"ast": null, "code": "var _jsxFileName = \"D:\\\\html\\\\training\\\\iti\\\\graduation_project\\\\src\\\\App.js\";\nimport './App.css';\nimport { Fragment } from 'react';\nimport Register from './pages/Register';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(Fragment, {\n    children: /*#__PURE__*/_jsxDEV(Register, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 7,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["Fragment", "Register", "jsxDEV", "_jsxDEV", "App", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/html/training/iti/graduation_project/src/App.js"], "sourcesContent": ["import './App.css';\nimport {Fragment} from 'react';\nimport Register from './pages/Register';\n\nfunction App() {\n  return (\n    <Fragment>\n      <Register />\n    </Fragment>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAO,WAAW;AAClB,SAAQA,QAAQ,QAAO,OAAO;AAC9B,OAAOC,QAAQ,MAAM,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACH,QAAQ;IAAAK,QAAA,eACPF,OAAA,CAACF,QAAQ;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEf;AAACC,EAAA,GANQN,GAAG;AAQZ,eAAeA,GAAG;AAAC,IAAAM,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}