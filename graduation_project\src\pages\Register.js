import React from 'react'

const Register = () =>{
  return (
    <section className='py-5 container'>
    <h1 className='text-center'>Register Now</h1>
<form className="row g-3 bg-dark mt-5 mb-5 mx-5 my-5 ">
<div className="col-md-6 col-lg-10">
  <div className="col">
  <label htmlFor="inputState" className="form-label text-light">First name</label>
    <input type="text" className="form-control" placeholder="First name" aria-label="First name" />
  </div>
  <div className="col">
  <label htmlFor="inputState" className="form-label text-light">Last name</label>
    <input type="text" className="form-control" placeholder="Last name" aria-label="Last name" />
  </div>
</div>

  <div className="col-md-6 col-lg-5">
    <label htmlFor="inputEmail4" className="form-label text-light">Email</label>
    <input type="email"  placeholder="<EMAIL>" className="form-control" id="inputEmail4" />
  </div>
  <div className="col-md-6 col-lg-5">
    <label htmlFor="inputPassword4" className="form-label text-light">Password</label>
    <input type="password" placeholder="Password" className="form-control" id="inputPassword4" />
  </div>

  <div className="col-md-6 col-lg-5">
    <label htmlFor="inputCity" className="form-label text-light">City</label>
    <input type="text" placeholder="City" className="form-control" id="inputCity" />
  </div>
  <div className="col-md-6 ">
    <label htmlFor="inputphone" className="form-label text-light">Phone</label>
    <input type="text" placeholder="Phone" className="form-control" id="inputCity" />
  </div>

<div className='mx-5'>
  <div className="form-check form-switch">
    <input className="form-check-input " type="checkbox" role="switch" id="flexSwitchCheckDefault" />
    <label className="form-check-label text-light" htmlFor="flexSwitchCheckDefault">Male</label>
    <br/>
    <input className="form-check-input " type="checkbox" role="switch" id="flexSwitchCheckDefault"  />
    <label className="form-check-label text-light" htmlFor="flexSwitchCheckDefault">FeMale</label>
  </div>
</div>



  <div className="col-12">
    <button type="submit" className="btn btn-primary mx-3">Sign in</button>

    <button type="submit" className="btn btn-danger"><a href="Sign.js">Sign up</a></button>
  </div>
  
</form>
</section>
  )
}

export default Register;