[{"D:\\html\\training\\iti\\graduation_project\\src\\index.js": "1", "D:\\html\\training\\iti\\graduation_project\\src\\App.js": "2", "D:\\html\\training\\iti\\graduation_project\\src\\reportWebVitals.js": "3", "D:\\html\\training\\iti\\graduation_project\\src\\pages\\Home.js": "4", "D:\\html\\training\\iti\\graduation_project\\src\\pages\\Register.js": "5"}, {"size": 369, "mtime": 1754247572629, "results": "6", "hashOfConfig": "7"}, {"size": 200, "mtime": 1754296839855, "results": "8", "hashOfConfig": "7"}, {"size": 362, "mtime": 1754218235610, "results": "9", "hashOfConfig": "7"}, {"size": 1147, "mtime": 1754295576489, "results": "10", "hashOfConfig": "7"}, {"size": 2430, "mtime": 1754298013908, "results": "11", "hashOfConfig": "7"}, {"filePath": "12", "messages": "13", "suppressedMessages": "14", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "yivh8v", {"filePath": "15", "messages": "16", "suppressedMessages": "17", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "18", "messages": "19", "suppressedMessages": "20", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "21", "messages": "22", "suppressedMessages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\html\\training\\iti\\graduation_project\\src\\index.js", [], [], "D:\\html\\training\\iti\\graduation_project\\src\\App.js", [], [], "D:\\html\\training\\iti\\graduation_project\\src\\reportWebVitals.js", [], [], "D:\\html\\training\\iti\\graduation_project\\src\\pages\\Home.js", ["27"], [], "D:\\html\\training\\iti\\graduation_project\\src\\pages\\Register.js", [], [], {"ruleId": "28", "severity": 1, "message": "29", "line": 15, "column": 33, "nodeType": "30", "endLine": 15, "endColumn": 45}, "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement"]