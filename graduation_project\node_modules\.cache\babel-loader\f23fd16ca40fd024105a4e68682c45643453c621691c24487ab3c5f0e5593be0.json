{"ast": null, "code": "var _jsxFileName = \"D:\\\\html\\\\training\\\\iti\\\\graduation_project\\\\src\\\\pages\\\\Home.js\";\nimport React from 'react';\nimport headerimg from '../Assets/header.jpg';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport { faCircle } from '@fortawesome/free-solid-svg-icons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Home = () => {\n  return /*#__PURE__*/_jsxDEV(\"header\", {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"row\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-md-6 col-lg-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n            children: \"We Provide All Health Care Solutions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 13,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"Protect Your Health And Take Care To Your Health\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 14,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            children: /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#\",\n              children: \"Read More\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 15,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 15,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"+\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 16,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 12,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-lg-6 col-md-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"header-box\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: headerimg,\n              alt: \"\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 20,\n              columnNumber: 25\n            }, this), /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n              icon: faCircle\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 21,\n              columnNumber: 25\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 19,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 11,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 9,\n    columnNumber: 9\n  }, this);\n};\n_c = Home;\nexport default Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");", "map": {"version": 3, "names": ["React", "headerimg", "FontAwesomeIcon", "faCircle", "jsxDEV", "_jsxDEV", "Home", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "href", "src", "alt", "icon", "_c", "$RefreshReg$"], "sources": ["D:/html/training/iti/graduation_project/src/pages/Home.js"], "sourcesContent": ["import React from 'react';\r\nimport headerimg from '../Assets/header.jpg';\r\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\r\nimport { faCircle} from '@fortawesome/free-solid-svg-icons';\r\n\r\n\r\nconst Home = () =>{\r\n    return (\r\n        <header>\r\n            <div className=\"container\">\r\n                <div className=\"row\">\r\n                    <div className=\"col-md-6 col-lg-6\">\r\n                        <h5>We Provide All Health Care Solutions</h5>\r\n                        <h2>Protect Your Health And Take Care To Your Health</h2>\r\n                        <button><a href=\"#\">Read More</a></button>\r\n                        <span>+</span>\r\n                    </div>\r\n                    <div className=\"col-lg-6 col-md-6\">\r\n                        <div className=\"header-box\">\r\n                        <img src={ headerimg } alt=\"\" />\r\n                        <FontAwesomeIcon icon={faCircle} />\r\n                        </div>  \r\n                        \r\n                    </div>\r\n                    </div>\r\n                    </div>\r\n                    </header>\r\n                    )\r\n        }\r\nexport default Home;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,SAAS,MAAM,sBAAsB;AAC5C,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,QAAQ,QAAO,mCAAmC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAG5D,MAAMC,IAAI,GAAGA,CAAA,KAAK;EACd,oBACID,OAAA;IAAAE,QAAA,eACIF,OAAA;MAAKG,SAAS,EAAC,WAAW;MAAAD,QAAA,eACtBF,OAAA;QAAKG,SAAS,EAAC,KAAK;QAAAD,QAAA,gBAChBF,OAAA;UAAKG,SAAS,EAAC,mBAAmB;UAAAD,QAAA,gBAC9BF,OAAA;YAAAE,QAAA,EAAI;UAAoC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7CP,OAAA;YAAAE,QAAA,EAAI;UAAgD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzDP,OAAA;YAAAE,QAAA,eAAQF,OAAA;cAAGQ,IAAI,EAAC,GAAG;cAAAN,QAAA,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC1CP,OAAA;YAAAE,QAAA,EAAM;UAAC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC,eACNP,OAAA;UAAKG,SAAS,EAAC,mBAAmB;UAAAD,QAAA,eAC9BF,OAAA;YAAKG,SAAS,EAAC,YAAY;YAAAD,QAAA,gBAC3BF,OAAA;cAAKS,GAAG,EAAGb,SAAW;cAACc,GAAG,EAAC;YAAE;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChCP,OAAA,CAACH,eAAe;cAACc,IAAI,EAAEb;YAAS;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAErB,CAAC;AAAAK,EAAA,GAtBHX,IAAI;AAuBV,eAAeA,IAAI;AAAC,IAAAW,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}